{"data": [{"id": "0931e5d9-fccc-459b-81a5-c1a251d16c7a", "name": "Google Document", "logo": "https://storage.googleapis.com/ruh-dev/mcp-logos/Google-Docs-logo.png/1750839188-Google-Docs-logo.png", "description": "Google Document MCP Server.", "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07", "user_ids": null, "owner_type": "user", "config": [{"url": "https://google-docs-mcp-dev-624209391722.us-central1.run.app/mcp", "type": "streamable-http"}], "git_url": null, "git_branch": null, "deployment_status": "pending", "visibility": "public", "tags": null, "status": "active", "created_at": "2025-06-24T13:00:48.696889", "updated_at": "2025-07-23T09:38:38.583242", "image_name": null, "category": "general", "mcp_tools_config": {"meta": null, "nextCursor": null, "tools": [{"name": "update_document", "description": "Update a Google Document with new content at a specific position", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}, "insert_at": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Insert At"}}, "required": ["document_id", "content"], "title": "UpdateDocument", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "verify_connection", "description": "Verify the connection to Google Docs API and display user info", "input_schema": {"properties": {}, "title": "VerifyConnection", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "append_document", "description": "Append content to the end of a Google Document", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "content": {"title": "Content", "type": "string"}}, "required": ["document_id", "content"], "title": "AppendDocument", "type": "object"}, "output_schema": {"profile": ""}, "annotations": null}, {"name": "insert_image", "description": "Insert an image into a Google Document at a specific position", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}, "image_url": {"title": "Image Url", "type": "string"}, "insert_index": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": null, "title": "Insert Index"}, "width": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 300, "title": "<PERSON><PERSON><PERSON>"}, "height": {"anyOf": [{"type": "integer"}, {"type": "null"}], "default": 200, "title": "Height"}, "alt_text": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Alt Text"}}, "required": ["document_id", "image_url"], "title": "InsertImage", "type": "object"}, "output_schema": null, "annotations": null}, {"name": "create_document", "description": "Create a new Google Document with optional title and content. Supports plain text, HTML, and Markdown formats.", "input_schema": {"properties": {"title": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Title"}, "content": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "title": "Content"}, "format": {"anyOf": [{"enum": ["plain", "html", "markdown"], "type": "string"}, {"type": "null"}], "default": "plain", "title": "Format"}}, "title": "CreateDocument", "type": "object"}, "output_schema": {}, "annotations": null}, {"name": "get_document", "description": "Retrieve the content of a Google Document by its ID", "input_schema": {"properties": {"document_id": {"title": "Document Id", "type": "string"}}, "required": ["document_id"], "title": "GetDocument", "type": "object"}, "output_schema": {"content": [{"type": "text", "text": "{\n  \"success\": true,\n  \"message\": \"Image inserted successfully\",\n  \"document_id\": \"1NiM26Hn0HqLy49Xc_yyCt5mUF8hrzAIE5rbMpGZzSK4\",\n  \"image_url\": \"https://images.pexels.com/photos/1566308/pexels-photo-1566308.jpeg\",\n  \"insert_index\": 20,\n  \"width\": 300,\n  \"height\": 200,\n  \"alt_text\": null,\n  \"url\": \"https://docs.google.com/document/d/1NiM26Hn0HqLy49Xc_yyCt5mUF8hrzAIE5rbMpGZzSK4/edit\"\n}"}], "isError": false}, "annotations": null}]}, "is_added": true, "env_keys": null, "component_category": "file_handling", "env_credential_status": "pending_input", "oauth_details": {"provider": "google", "tool_name": "google_document"}}], "metadata": {"total": 1, "totalPages": 1, "currentPage": 1, "pageSize": 10, "hasNextPage": false, "hasPreviousPage": false}}