Updated component state for MCP_script-generation-mcp_script_generate-1753329143503, input video_type: SHORTs
InspectorContext.tsx:319 Updating node MCP_script-generation-mcp_script_generate-1753329143503 with new config for video_type: SHORTs
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329143503', inputName: 'topic'}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
MCPMarketplaceComponent.tsx:114 Stored config in component state for node MCP_script-generation-mcp_script_generate-1753329143503: {script_type: 'TOPIC', video_type: 'SHORT'}
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}