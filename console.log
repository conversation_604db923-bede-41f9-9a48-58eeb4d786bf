Updated component state for MCP_script-generation-mcp_script_generate-1753328656538, input script_type: TOPICk
InspectorContext.tsx:319 Updating node MCP_script-generation-mcp_script_generate-1753328656538 with new config for script_type: TOPICk
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753328656538', inputName: 'topic'}componentType: "MCP_script-generation-mcp_script_generate"inputName: "topic"inputType: "string"nodeId: "MCP_script-generation-mcp_script_generate-1753328656538"[[Prototype]]: Object
MCPMarketplaceComponent.tsx:114 Stored config in component state for node MCP_script-generation-mcp_script_generate-1753328656538: {script_type: 'TOPIC', video_type: 'SHORT'}script_type: "TOPIC"video_type: "SHORT"[[Prototype]]: Object
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']0: "AI"1: "Logic"2: "Data Interaction"3: "Helpers"4: "IO"5: "Processing"6: "communication"7: "social_media"8: "notifications_alerts"9: "Tools"10: "database"11: "cloud_storage"12: "file_handling"13: "Social Media"length: 14[[Prototype]]: Array(0)
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {name: 'MCP_MetaAds_create_ad', display_name: 'MetaAds - create_ad', description: 'Create a new ad with an existing creative', category: 'social_media', icon: 'Cloud', …}1: {name: 'MCP_MetaAds_create_ad_creative', display_name: 'MetaAds - create_ad_creative', description: 'Create a new ad creative using an uploaded image hash', category: 'social_media', icon: 'Cloud', …}2: {name: 'MCP_MetaAds_create_adset', display_name: 'MetaAds - create_adset', description: 'Create a new ad set in a Meta Ads account', category: 'social_media', icon: 'Cloud', …}3: {name: 'MCP_MetaAds_create_budget_schedule', display_name: 'MetaAds - create_budget_schedule', description: 'Create a budget schedule for a Meta Ads campaign', category: 'social_media', icon: 'Cloud', …}4: {name: 'MCP_MetaAds_create_meta_campaign', display_name: 'MetaAds - create_meta_campaign', description: 'Create a Meta Ads campaign with specified configuration', category: 'social_media', icon: 'Cloud', …}5: {name: 'MCP_MetaAds_gather_ad_requirements', display_name: 'MetaAds - gather_ad_requirements', description: 'Comprehensive tool to gather all requirements for …eating Meta Ads campaigns, ad sets, and creatives', category: 'social_media', icon: 'Cloud', …}6: {name: 'MCP_MetaAds_get_account_info', display_name: 'MetaAds - get_account_info', description: 'Get detailed information about a specific ad account', category: 'social_media', icon: 'Cloud', …}7: {name: 'MCP_MetaAds_get_account_pages', display_name: 'MetaAds - get_account_pages', description: 'Get pages associated with a Meta Ads account', category: 'social_media', icon: 'Cloud', …}8: {name: 'MCP_MetaAds_get_ad_account_insights', display_name: 'MetaAds - get_ad_account_insights', description: 'Get comprehensive ad account insights including customer behavior and demographic breakdowns', category: 'social_media', icon: 'Cloud', …}9: {name: 'MCP_MetaAds_get_ad_accounts', display_name: 'MetaAds - get_ad_accounts', description: 'Get ad accounts accessible by a user', category: 'social_media', icon: 'Cloud', …}10: {name: 'MCP_MetaAds_get_ad_creatives', display_name: 'MetaAds - get_ad_creatives', description: 'Get creative details for a specific ad', category: 'social_media', icon: 'Cloud', …}11: {name: 'MCP_MetaAds_get_ad_details', display_name: 'MetaAds - get_ad_details', description: 'Get detailed information about a specific ad', category: 'social_media', icon: 'Cloud', …}12: {name: 'MCP_MetaAds_get_ad_image', display_name: 'MetaAds - get_ad_image', description: 'Get, download, and visualize a Meta ad image in one step', category: 'social_media', icon: 'Cloud', …}13: {name: 'MCP_MetaAds_get_ads', display_name: 'MetaAds - get_ads', description: 'Get ads for a Meta Ads account with optional filtering', category: 'social_media', icon: 'Cloud', …}14: {name: 'MCP_MetaAds_get_adset_details', display_name: 'MetaAds - get_adset_details', description: 'Get detailed information about a specific ad set', category: 'social_media', icon: 'Cloud', …}15: {name: 'MCP_MetaAds_get_adsets', display_name: 'MetaAds - get_adsets', description: 'Get ad sets for a Meta Ads account with optional filtering by campaign', category: 'social_media', icon: 'Cloud', …}16: {name: 'MCP_MetaAds_get_audience_demographics', display_name: 'MetaAds - get_audience_demographics', description: 'Get detailed demographic information about your au…es including age, gender, location, and interests', category: 'social_media', icon: 'Cloud', …}17: {name: 'MCP_MetaAds_get_audience_insights', display_name: 'MetaAds - get_audience_insights', description: 'Get detailed insights and analytics for a specific…udience including size estimates and demographics', category: 'social_media', icon: 'Cloud', …}18: {name: 'MCP_MetaAds_get_campaign_details', display_name: 'MetaAds - get_campaign_details', description: 'Get detailed information about a specific campaign', category: 'social_media', icon: 'Cloud', …}19: {name: 'MCP_MetaAds_get_campaign_insights', display_name: 'MetaAds - get_campaign_insights', description: 'Get historical campaign performance data including customer engagement metrics and conversion data', category: 'social_media', icon: 'Cloud', …}20: {name: 'MCP_MetaAds_get_campaigns', display_name: 'MetaAds - get_campaigns', description: 'Get campaigns for a Meta Ads account with optional filtering', category: 'social_media', icon: 'Cloud', …}21: {name: 'MCP_MetaAds_get_conversion_data', display_name: 'MetaAds - get_conversion_data', description: 'Get detailed conversion tracking data to understand customer actions and purchase behavior', category: 'social_media', icon: 'Cloud', …}22: {name: 'MCP_MetaAds_get_custom_audiences', display_name: 'MetaAds - get_custom_audiences', description: 'Get custom audiences including email lists, purchase data, and website visitor audiences', category: 'social_media', icon: 'Cloud', …}23: {name: 'MCP_MetaAds_get_customer_segments', display_name: 'MetaAds - get_customer_segments', description: 'Get customer segments and audience categorization data for targeted marketing', category: 'social_media', icon: 'Cloud', …}24: {name: 'MCP_MetaAds_get_insights', display_name: 'MetaAds - get_insights', description: 'Get performance insights for a campaign, ad set, ad or account', category: 'social_media', icon: 'Cloud', …}25: {name: 'MCP_MetaAds_get_lookalike_audiences', display_name: 'MetaAds - get_lookalike_audiences', description: 'Get lookalike audiences that are similar to your existing customers', category: 'social_media', icon: 'Cloud', …}26: {name: 'MCP_MetaAds_get_pixel_events', display_name: 'MetaAds - get_pixel_events', description: 'Get Facebook Pixel events data to track customer interactions and website behavior', category: 'social_media', icon: 'Cloud', …}27: {name: 'MCP_MetaAds_update_ad', display_name: 'MetaAds - update_ad', description: 'Update an ad with new settings', category: 'social_media', icon: 'Cloud', …}28: {name: 'MCP_MetaAds_update_adset', display_name: 'MetaAds - update_adset', description: 'Update an ad set with new settings including frequency caps', category: 'social_media', icon: 'Cloud', …}29: {name: 'MCP_MetaAds_update_campaign_status', display_name: 'MetaAds - update_campaign_status', description: 'Update the status of a Meta Ads campaign (ACTIVE, PAUSED, etc.)', category: 'social_media', icon: 'Cloud', …}30: {name: 'MCP_MetaAds_update_meta_campaign', display_name: 'MetaAds - update_meta_campaign', description: 'Update an existing Meta Ads campaign with new configuration', category: 'social_media', icon: 'Cloud', …}31: {name: 'MCP_MetaAds_upload_ad_image', display_name: 'MetaAds - upload_ad_image', description: 'Upload an image to use in Meta Ads creatives', category: 'social_media', icon: 'Cloud', …}32: {name: 'MCP_script-generation-mcp_research', display_name: 'script-generation-mcp - research', description: 'Research for the given topic', category: 'social_media', icon: 'Cloud', …}33: {name: 'MCP_script-generation-mcp_script_generate', display_name: 'script-generation-mcp - script_generate', description: 'Provide topic and keyword to generator Script', category: 'social_media', icon: 'Cloud', …}34: {name: 'MCP_stock-image-generation-mcp_fetch_stock_images', display_name: 'stock-image-generation-mcp - fetch_stock_images', description: 'fetch the stock image using the script', category: 'social_media', icon: 'Cloud', …}35: {name: 'MCP_stock-image-generation-mcp_generate_ai_stock_image', display_name: 'stock-image-generation-mcp - generate_ai_stock_image', description: 'generate and find the stock image for the video', category: 'social_media', icon: 'Cloud', …}36: {name: 'MCP_stock-image-generation-mcp_generate_image', display_name: 'stock-image-generation-mcp - generate_image', description: 'generate the image using the script', category: 'social_media', icon: 'Cloud', …}37: {name: 'MCP_stock-image-generation-mcp_generate_stock_image', display_name: 'stock-image-generation-mcp - generate_stock_image', description: 'generate and find the stock image for the video', category: 'social_media', icon: 'Cloud', …}38: {name: 'MCP_stock-video-generation-mcp_fetch_stock_videos', display_name: 'stock-video-generation-mcp - fetch_stock_videos', description: 'fetch the stock videos from search terms', category: 'social_media', icon: 'Cloud', …}39: {name: 'MCP_stock-video-generation-mcp_generate_ai_stock_video', display_name: 'stock-video-generation-mcp - generate_ai_stock_video', description: 'generate the ai stock video using the script', category: 'social_media', icon: 'Cloud', …}40: {name: 'MCP_stock-video-generation-mcp_generate_stock_video', display_name: 'stock-video-generation-mcp - generate_stock_video', description: 'generate and find the stock video for the video', category: 'social_media', icon: 'Cloud', …}41: {name: 'MCP_Webflow_collection_fields_create_option', display_name: 'Webflow - collection_fields_create_option', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}42: {name: 'MCP_Webflow_collection_fields_create_reference', display_name: 'Webflow - collection_fields_create_reference', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}43: {name: 'MCP_Webflow_collection_fields_create_static', display_name: 'Webflow - collection_fields_create_static', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}44: {name: 'MCP_Webflow_collection_fields_update', display_name: 'Webflow - collection_fields_update', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}45: {name: 'MCP_Webflow_collections_create', display_name: 'Webflow - collections_create', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}46: {name: 'MCP_Webflow_collections_get', display_name: 'Webflow - collections_get', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}47: {name: 'MCP_Webflow_collections_items_create_item', display_name: 'Webflow - collections_items_create_item', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}48: {name: 'MCP_Webflow_collections_items_create_item_live', display_name: 'Webflow - collections_items_create_item_live', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}49: {name: 'MCP_Webflow_collections_items_list_items', display_name: 'Webflow - collections_items_list_items', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}50: {name: 'MCP_Webflow_collections_items_publish_items', display_name: 'Webflow - collections_items_publish_items', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}51: {name: 'MCP_Webflow_collections_items_update_items', display_name: 'Webflow - collections_items_update_items', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}52: {name: 'MCP_Webflow_collections_items_update_items_live', display_name: 'Webflow - collections_items_update_items_live', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}53: {name: 'MCP_Webflow_collections_list', display_name: 'Webflow - collections_list', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}54: {name: 'MCP_Webflow_pages_get_content', display_name: 'Webflow - pages_get_content', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}55: {name: 'MCP_Webflow_pages_get_metadata', display_name: 'Webflow - pages_get_metadata', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}56: {name: 'MCP_Webflow_pages_list', display_name: 'Webflow - pages_list', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}57: {name: 'MCP_Webflow_pages_update_page_settings', display_name: 'Webflow - pages_update_page_settings', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}58: {name: 'MCP_Webflow_pages_update_static_content', display_name: 'Webflow - pages_update_static_content', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}59: {name: 'MCP_Webflow_sites_get', display_name: 'Webflow - sites_get', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}60: {name: 'MCP_Webflow_sites_list', display_name: 'Webflow - sites_list', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}61: {name: 'MCP_Webflow_sites_publish', display_name: 'Webflow - sites_publish', description: 'Webflow Server', category: 'social_media', icon: 'Cloud', …}length: 62[[Prototype]]: Array(0)
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]0: {name: 'AlterMetadataComponent', display_name: 'Alter Metadata', description: 'Modifies metadata dictionary keys.', category: 'Processing', icon: 'Tag', …}1: {name: 'CombineTextComponent', display_name: 'Combine Text', description: 'Joins text inputs with a separator, supporting a v…E__, __TAB__, __NEWLINE__ for special characters.', category: 'Processing', icon: 'Link', …}2: {name: 'DataToDataFrameComponent', display_name: 'Data to DataFrame', description: 'Converts data to a Pandas DataFrame.', category: 'Processing', icon: 'Table', …}3: {name: 'MergeDataComponent', display_name: 'Merge Data', description: 'Combines multiple dictionaries or lists.', category: 'Processing', icon: 'Combine', …}4: {name: 'MessageToDataComponent', display_name: 'Message To Data', description: 'Extracts fields from a Message object.', category: 'Processing', icon: 'Package', …}5: {name: 'RegexExtractorComponent', display_name: 'Regex Extractor', description: 'Extract data from text using regular expressions', category: 'Processing', icon: 'Search', …}6: {name: 'SelectDataComponent', display_name: 'Select Data', description: 'Extracts elements from lists or dictionaries.', category: 'Processing', icon: 'Filter', …}7: {name: 'SplitTextComponent', display_name: 'Split Text', description: 'Splits text into a list using a delimiter.', category: 'Processing', icon: 'Scissors', …}8: {name: 'UniversalConverterComponent', display_name: 'Universal Converter', description: 'Convert data between different types (JSON, CSV, String, Number, Boolean, etc.)', category: 'Processing', icon: 'ArrowRightLeft', …}9: {name: 'DelayComponent', display_name: 'Wait / Delay', description: 'Pauses the workflow execution for a set number of seconds.', category: 'Processing', icon: 'Timer', …}length: 10[[Prototype]]: Array(0)
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']0: "AI"1: "Logic"2: "Data Interaction"3: "Helpers"4: "IO"5: "Processing"6: "communication"7: "social_media"8: "notifications_alerts"9: "Tools"10: "database"11: "cloud_storage"12: "file_handling"13: "Social Media"length: 14[[Prototype]]: Array(0)
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51