Updated component state for MCP_script-generation-mcp_script_generate-1753329566645, input video_type: SHORTs
InspectorContext.tsx:319 Updating node MCP_script-generation-mcp_script_generate-1753329566645 with new config for video_type: SHORTs
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input topic with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input script_type with default: TOPIC
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input keywords with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input video_type with default: SHORT
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input link with default: null
MCPMarketplaceComponent.tsx:93 🔍 [MCP CONFIG] Setting value for input video_type: {inputName: 'video_type', isHandle: true, oldValue: 'SHORT', newValue: 'SHORT'}
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input topic with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input script_type with default: TOPIC
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input keywords with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input video_type with default: SHORT
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input link with default: null
MCPMarketplaceComponent.tsx:93 🔍 [MCP CONFIG] Setting value for input video_type: {inputName: 'video_type', isHandle: true, oldValue: 'SHORT', newValue: 'SHORT'}
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
fieldValidation.ts:621 [2025-07-24 03:59:47] [isFieldRequired] Checking if field is required (cache miss):
    - Node: MCP_script-generation-mcp_script_generate-1753329566645 (script-generation-mcp - script_generate)
    - Field: topic (Topic)
    - Input type: string
    - Explicitly required: YES
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:680 [2025-07-24 03:59:47] [isFieldRequired] Field topic is a handle input but NOT connected, required for direct input
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] topic: {inputName: 'topic', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
inputSystemConfig.ts:342 [Input System Migration] Dynamic system used {inputType: 'string', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', inputName: 'topic'}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] topic: {inputName: 'topic', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
fieldValidation.ts:621 [2025-07-24 03:59:47] [isFieldRequired] Checking if field is required (cache miss):
    - Node: MCP_script-generation-mcp_script_generate-1753329566645 (script-generation-mcp - script_generate)
    - Field: script_type (script type)
    - Input type: string
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 03:59:47] [isFieldRequired] Field script_type is a handle input and not explicitly required, not required for direct input
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] script_type: {inputName: 'script_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] script_type: {inputName: 'script_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
fieldValidation.ts:621 [2025-07-24 03:59:47] [isFieldRequired] Checking if field is required (cache miss):
    - Node: MCP_script-generation-mcp_script_generate-1753329566645 (script-generation-mcp - script_generate)
    - Field: keywords (keywords)
    - Input type: string
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 03:59:47] [isFieldRequired] Field keywords is a handle input and not explicitly required, not required for direct input
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] keywords: {inputName: 'keywords', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] keywords: {inputName: 'keywords', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
fieldValidation.ts:621 [2025-07-24 03:59:47] [isFieldRequired] Checking if field is required (cache miss):
    - Node: MCP_script-generation-mcp_script_generate-1753329566645 (script-generation-mcp - script_generate)
    - Field: video_type (video type)
    - Input type: string
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 03:59:47] [isFieldRequired] Field video_type is a handle input and not explicitly required, not required for direct input
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] video_type: {inputName: 'video_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] video_type: {inputName: 'video_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
fieldValidation.ts:621 [2025-07-24 03:59:47] [isFieldRequired] Checking if field is required (cache miss):
    - Node: MCP_script-generation-mcp_script_generate-1753329566645 (script-generation-mcp - script_generate)
    - Field: link (Link)
    - Input type: string
    - Explicitly required: NO
    - Is handle: YES
    - Is handle connected: NO
    - Ends with _handle: NO
    - Has requirement rules: NO
fieldValidation.ts:694 [2025-07-24 03:59:47] [isFieldRequired] Field link is a handle input and not explicitly required, not required for direct input
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] link: {inputName: 'link', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] link: {inputName: 'link', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
MCPMarketplaceComponent.tsx:131 Stored config in component state for node MCP_script-generation-mcp_script_generate-1753329566645: {script_type: 'TOPIC', video_type: 'SHORT', keywords: ''}
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input topic with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input script_type with default: TOPIC
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input keywords with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input video_type with default: SHORT
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input link with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input topic with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input script_type with default: TOPIC
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input keywords with default: null
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input video_type with default: SHORT
MCPMarketplaceComponent.tsx:75 🔍 [MCP CONFIG] Skipping handle input link with default: null
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] topic: {inputName: 'topic', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] topic: {inputName: 'topic', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] script_type: {inputName: 'script_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] script_type: {inputName: 'script_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] keywords: {inputName: 'keywords', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] keywords: {inputName: 'keywords', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] video_type: {inputName: 'video_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] video_type: {inputName: 'video_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] link: {inputName: 'link', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] link: {inputName: 'link', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:473 Sidebar component categories: (14) ['AI', 'Logic', 'Data Interaction', 'Helpers', 'IO', 'Processing', 'communication', 'social_media', 'notifications_alerts', 'Tools', 'database', 'cloud_storage', 'file_handling', 'Social Media']
Sidebar.tsx:479 MCP category does not exist in components
Sidebar.tsx:1486 components (8) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components (62) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components [{…}]
Sidebar.tsx:1486 components (2) [{…}, {…}]
Sidebar.tsx:1486 components (10) [{…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}, {…}]
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] topic - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] script_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] keywords - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] video_type - OPTIMIZED VERSION WORKING
inputVisibility.ts:51 ✅ [CACHE-HIT] link - OPTIMIZED VERSION WORKING
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] topic: {inputName: 'topic', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] topic: {inputName: 'topic', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] script_type: {inputName: 'script_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] script_type: {inputName: 'script_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] script_type: {inputName: 'script_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] keywords: {inputName: 'keywords', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] keywords: {inputName: 'keywords', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
useConnectedHandles.ts:109 🔍 [DISABLE DEBUG] video_type: {inputName: 'video_type', isDirectlyConnected: false, connectedInputs: Array(0), allConnectedInputs: {…}, willDisable: false}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] video_type: {inputName: 'video_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] video_type: {inputName: 'video_type', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] link: {inputName: 'link', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}
InputRenderer.tsx:78 🔍 [INPUT RENDERER DEBUG] link: {inputName: 'link', componentType: 'MCP_script-generation-mcp_script_generate', nodeId: 'MCP_script-generation-mcp_script_generate-1753329566645', isDisabled: false, isConnected: false, …}