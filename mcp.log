2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - INFO - [send_result] Sending result to topic 'mcp_results': {'request_id': '7aae0d37-31e8-4067-8128-7963b6787e93', 'result': ["Input validation error: '1000' is not valid under any of the given schemas"], 'mcp_status': 'success', 'timestamp': '2025-07-21T05:00:54.725769+00:00'}
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - INFO - [process_message] :white_check_mark: Successfully processed request offset=10352 for tool 'create_adset' (mcp_id='5a1e596c-5c2c-4146-8baa-6dfce277f4ce', user_id='5229e05b-aaea-4850-9972-7268559318cf'). Result type: <class 'list'>
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - INFO - [execute_tool] :white_check_mark: Tool execution completed successfully
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - WARNING - [_execute_url_tool] Content item is not valid JSON, returning as raw text: Input validation error: '1000' is not valid under any of the given schemas...
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.client.MCPClient - INFO - [close] MCP client connection closed successfully
2025-07-21 10:30:54 
2025-07-21 05:00:54 - httpx - INFO - [_send_single_request] HTTP Request: DELETE https://meta-mcp-dev-************.us-central1.run.app/mcp "HTTP/1.1 405 Method Not Allowed"
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.client.MCPClient - INFO - [close] Closing MCP client connection
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - INFO - [_execute_url_tool] MCP result type: <class 'mcp.types.CallToolResult'>
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - INFO - [_execute_url_tool] MCP result raw: meta=None content=[TextContent(type='text', text="Input validation error: '1000' is not valid under any of the given schemas", annotations=None, meta=None)] structuredContent=None isError=True
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.kafka_service - INFO - [_execute_url_tool] MCP tool call completed!
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.client.MCPClient - INFO - [call_tool] Successfully called tool: create_adset
2025-07-21 10:30:54 
2025-07-21 05:00:54 - httpx - INFO - [_send_single_request] HTTP Request: POST https://meta-mcp-dev-************.us-central1.run.app/mcp "HTTP/1.1 200 OK"
2025-07-21 10:30:54 
2025-07-21 05:00:54 - app.core_.client.MCPClient - INFO - [call_tool] Using read timeout for tool call: 0:15:00
2025-07-21 10:30:54 
2025-07-21 05:00:54 - httpx - INFO - [_send_single_request] HTTP Request: POST https://meta-mcp-dev-************.us-central1.run.app/mcp "HTTP/1.1 200 OK"
2025-07-21 10:30:54 
2025-07-21 05:00:54 - httpx - INFO - [_send_single_request] HTTP Request: GET https://meta-mcp-dev-************.us-central1.run.app/mcp "HTTP/1.1 405 Method Not Allowed"
2025-07-21 10:30:54 
2025-07-21 05:00:54 - httpx - INFO - [_send_single_request] HTTP Request: POST https://meta-mcp-dev-************.us-central1.run.app/mcp "HTTP/1.1 202 Accepted"
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] Using tool call read_timeout: 0:15:00
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] About to call MCP tool...
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] Tool parameters: {'campaign_id': '120227496564000727', 'name': 'Solar Power Bank', 'status': 'PAUSED', 'daily_budget': '1000', 'targeting': {'geo_locations': {'countries': ['US']}, 'publisher_platforms': ['facebook'], 'facebook_positions': ['feed']}, 'optimization_goal': 'LINK_CLICKS', 'billing_event': 'IMPRESSIONS', 'bid_strategy': 'LOWEST_COST_WITHOUT_CAP'}
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] Executing tool: create_adset
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] Sending result to topic 'workflow-responses': {'result': 'Connected to MCP server', 'message': 'Connected to MCP server', 'status': 'connected', 'node_label': 'MetaAds - create_adset', 'transition_id': 'transition-MCP_MetaAds_create_adset-*************', 'workflow_status': 'running'}
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] Connected to URL server at https://meta-mcp-dev-************.us-central1.run.app/mcp (Attempt 1/3)
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [_connect_with_retry] Successfully connected to MCP server after 1 attempts
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [_establish_connection] MCP session initialized successfully
2025-07-21 10:30:53 
2025-07-21 05:00:53 - mcp.client.streamable_http - INFO - [_maybe_extract_protocol_version_from_message] Negotiated protocol version: 2025-06-18
2025-07-21 10:30:53 
2025-07-21 05:00:53 - mcp.client.streamable_http - INFO - [_maybe_extract_session_id_from_response] Received session ID: 2ca7db21eff440b9a17f1a856feda614
2025-07-21 10:30:53 
2025-07-21 05:00:53 - httpx - INFO - [_send_single_request] HTTP Request: POST https://meta-mcp-dev-************.us-central1.run.app/mcp "HTTP/1.1 200 OK"
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [custom_httpx_client_factory] Creating httpx client with timeout: connect=30.0s, read=900.0s
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [_establish_connection] Creating Streamable HTTP client with connection_timeout: 30.0s, sse_read_timeout: 900.0s
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [_connect_with_retry] Connecting to MCP server (attempt 1/2)
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.services.credential_service - INFO - [__init__] CredentialService initialized with API base URL: https://app-dev.rapidinnovation.dev
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [__init__]    connection_type: streamable_http
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [__init__]    container_command: None
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [__init__]    docker_image: mcp-text-server
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.client.MCPClient - INFO - [__init__] :wrench: MCPClient initialized with:
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.oauth_client_factory - INFO - [create_oauth_aware_client] Creating standard MCP client (no oauth_details found)
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.oauth_client_factory - INFO - [create_legacy_compatible_client] Using OAuth discovery flow
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [_execute_url_tool] Using legacy authentication flow for 5a1e596c-5c2c-4146-8baa-6dfce277f4ce
2025-07-21 10:30:53 
2025-07-21 05:00:53 - mcp_executor_metrics - INFO - [_log_metric] METRIC: {'metric_type': 'timer', 'operation': 'fetch', 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'duration_ms': 378.1431440729648, 'cache_hit': None, 'error_type': None, 'timestamp': '2025-07-21T05:00:53.207710'}
2025-07-21 10:30:53 
2025-07-21 05:00:53,207 - mcp_executor_metrics - INFO - METRIC: {'metric_type': 'timer', 'operation': 'fetch', 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'duration_ms': 378.1431440729648, 'cache_hit': None, 'error_type': None, 'timestamp': '2025-07-21T05:00:53.207710'}
2025-07-21 10:30:53 
2025-07-21 05:00:53 - app.core_.kafka_service - INFO - [fetch_mcp_config] Successfully fetched MCP config for 5a1e596c-5c2c-4146-8baa-6dfce277f4ce
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [fetch_mcp_config] Fetching MCP configuration from: https://app-dev.rapidinnovation.dev/api/v1/mcps/orchestration/5a1e596c-5c2c-4146-8baa-6dfce277f4ce
2025-07-21 10:30:52 
2025-07-21 05:00:52 - mcp_executor_metrics - INFO - [_log_metric] METRIC: {'metric_type': 'counter', 'execution_type': 'url', 'status': 'started', 'duration_ms': None, 'attempt_count': None, 'error_type': None, 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'user_id': '5229e05b-aaea-4850-9972-7268559318cf', 'tool_name': 'create_adset', 'timestamp': '2025-07-21T05:00:52.827696'}
2025-07-21 10:30:52 
2025-07-21 05:00:52,827 - mcp_executor_metrics - INFO - METRIC: {'metric_type': 'counter', 'execution_type': 'url', 'status': 'started', 'duration_ms': None, 'attempt_count': None, 'error_type': None, 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'user_id': '5229e05b-aaea-4850-9972-7268559318cf', 'tool_name': 'create_adset', 'timestamp': '2025-07-21T05:00:52.827696'}
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [_execute_url_tool] :globe_with_meridians: Executing URL-based tool 'create_adset' on https://meta-mcp-dev-************.us-central1.run.app/mcp
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [execute_tool] :globe_with_meridians: Using URL execution with: https://meta-mcp-dev-************.us-central1.run.app/mcp
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [execute_tool] :first_place_medal: Selected streamable-http (highest priority)
2025-07-21 10:30:52 
2025-07-21 05:00:52 - mcp_executor_metrics - INFO - [_log_metric] METRIC: {'metric_type': 'timer', 'operation': 'fetch', 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'duration_ms': 296.99695704039186, 'cache_hit': None, 'error_type': None, 'timestamp': '2025-07-21T05:00:52.806989'}
2025-07-21 10:30:52 
2025-07-21 05:00:52,807 - mcp_executor_metrics - INFO - METRIC: {'metric_type': 'timer', 'operation': 'fetch', 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'duration_ms': 296.99695704039186, 'cache_hit': None, 'error_type': None, 'timestamp': '2025-07-21T05:00:52.806989'}
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [fetch_mcp_config] Successfully fetched MCP config for 5a1e596c-5c2c-4146-8baa-6dfce277f4ce
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [fetch_mcp_config] Fetching MCP configuration from: https://app-dev.rapidinnovation.dev/api/v1/mcps/orchestration/5a1e596c-5c2c-4146-8baa-6dfce277f4ce
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [execute_tool] :rocket: execute_tool called - mcp_id: 5a1e596c-5c2c-4146-8baa-6dfce277f4ce, user_id: 5229e05b-aaea-4850-9972-7268559318cf, tool: create_adset
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [process_message] :rocket: Executing tool 'create_adset' for mcp_id='5a1e596c-5c2c-4146-8baa-6dfce277f4ce', user_id='5229e05b-aaea-4850-9972-7268559318cf'
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [process_message] :white_check_mark: Required parameters extracted successfully - mcp_id: 5a1e596c-5c2c-4146-8baa-6dfce277f4ce, user_id: 5229e05b-aaea-4850-9972-7268559318cf, tool_name: create_adset, tool_parameters keys: ['campaign_id', 'name', 'status', 'daily_budget', 'targeting', 'optimization_goal', 'billing_event', 'bid_strategy']
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [process_message] Processing request from offset 10352: {'tool_name': 'create_adset', 'tool_parameters': {'campaign_id': '120227496564000727', 'name': 'Solar Power Bank', 'status': 'PAUSED', 'daily_budget': '1000', 'targeting': {'geo_locations': {'countries': ['US']}, 'publisher_platforms': ['facebook'], 'facebook_positions': ['feed']}, 'optimization_goal': 'LINK_CLICKS', 'billing_event': 'IMPRESSIONS', 'bid_strategy': 'LOWEST_COST_WITHOUT_CAP'}, 'request_id': '7aae0d37-31e8-4067-8128-7963b6787e93', 'correlation_id': '5a399d69-57c7-48a1-a4f9-c7934aaaaab2-****************', 'user_id': '5229e05b-aaea-4850-9972-7268559318cf', 'mcp_id': '5a1e596c-5c2c-4146-8baa-6dfce277f4ce', 'transition_id': 'transition-MCP_MetaAds_create_adset-*************', 'node_label': 'MetaAds - create_adset'}
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [process_message] Processing message: ConsumerRecord(topic='mcp-execution-request', partition=0, offset=10352, timestamp=*************, timestamp_type=0, key=None, value=b'{"tool_name": "create_adset", "tool_parameters": {"campaign_id": "120227496564000727", "name": "Solar Power Bank", "status": "PAUSED", "daily_budget": "1000", "targeting": {"geo_locations": {"countries": ["US"]}, "publisher_platforms": ["facebook"], "facebook_positions": ["feed"]}, "optimization_goal": "LINK_CLICKS", "billing_event": "IMPRESSIONS", "bid_strategy": "LOWEST_COST_WITHOUT_CAP"}, "request_id": "7aae0d37-31e8-4067-8128-7963b6787e93", "correlation_id": "5a399d69-57c7-48a1-a4f9-c7934aaaaab2-****************", "user_id": "5229e05b-aaea-4850-9972-7268559318cf", "mcp_id": "5a1e596c-5c2c-4146-8baa-6dfce277f4ce", "transition_id": "transition-MCP_MetaAds_create_adset-*************", "node_label": "MetaAds - create_adset"}', checksum=None, serialized_key_size=-1, serialized_value_size=734, headers=())
2025-07-21 10:30:52 
2025-07-21 05:00:52 - app.core_.kafka_service - INFO - [process_message] Semaphore released early. Current queue size: 10[2025-07-21T07:08:32.167Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-21T07:08:32.168Z] INFO: Starting JIRA MCP server
[2025-07-21T08:13:15.629Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-21T08:13:15.630Z] INFO: Starting JIRA MCP server
[2025-07-21T08:32:46.425Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-21T08:32:46.427Z] INFO: Starting JIRA MCP server
[2025-07-21T13:57:49.573Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-21T13:57:49.574Z] INFO: Starting JIRA MCP server
[2025-07-22T03:38:30.914Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-22T03:38:30.915Z] INFO: Starting JIRA MCP server
[2025-07-22T04:21:56.156Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-22T04:21:56.158Z] INFO: Starting JIRA MCP server
[2025-07-22T08:14:06.169Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-22T08:14:06.170Z] INFO: Starting JIRA MCP server
[2025-07-22T08:38:02.898Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-22T08:38:02.899Z] INFO: Starting JIRA MCP server
[2025-07-22T13:33:09.928Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-22T13:33:09.929Z] INFO: Starting JIRA MCP server
[2025-07-23T04:33:47.409Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-23T04:33:47.410Z] INFO: Starting JIRA MCP server
[2025-07-23T08:41:59.810Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-23T08:41:59.811Z] INFO: Starting JIRA MCP server
[2025-07-23T11:57:21.112Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-23T11:57:21.113Z] INFO: Starting JIRA MCP server
[2025-07-23T12:05:45.251Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-23T12:05:45.252Z] INFO: Starting JIRA MCP server
[2025-07-23T12:50:06.450Z] WARNING: JIRA client not configured - missing environment variables | Data: {"missing":["JIRA_BASE_URL","JIRA_API_TOKEN"]}
[2025-07-23T12:50:06.451Z] INFO: Starting JIRA MCP server
