import { useCallback, useEffect, useState } from "react";
import { Edge, Node } from "reactflow";
import { InputDefinition, WorkflowNodeData } from "@/types";

interface ConnectedHandlesResult {
  connectedInputs: Record<string, boolean>;
  isInputConnected: (inputName: string) => boolean;
  shouldDisableInput: (inputName: string) => boolean;
  getConnectedSource: (inputName: string) => { nodeId: string; handleId: string } | null;
  getConnectionInfo: (inputName: string) => {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeType?: string;
    sourceNodeLabel?: string;
  };
}

/**
 * Custom hook to track which handles of a node are connected
 * @param selectedNode The currently selected node
 * @param edges All edges in the workflow
 * @param nodes All nodes in the workflow
 * @returns An object with the connected input handles and helper functions
 */
export function useConnectedHandles(
  selectedNode: Node<WorkflowNodeData> | null,
  edges: Edge[],
  nodes: Node<WorkflowNodeData>[],
): ConnectedHandlesResult {
  // State to track connected input handles
  const [connectedInputs, setConnectedInputs] = useState<Record<string, boolean>>({});
  // State to track connection sources
  const [connectionSources, setConnectionSources] = useState<
    Record<string, { nodeId: string; handleId: string }>
  >({});

  // Update connected inputs whenever the selected node or edges change
  useEffect(() => {
    if (!selectedNode) {
      setConnectedInputs({});
      setConnectionSources({});
      return;
    }

    // Find all edges that target the selected node
    const nodeInputEdges = edges.filter((edge) => edge.target === selectedNode.id);

    // Create a map of input handle names to connection status
    const connectedHandles: Record<string, boolean> = {};
    const sources: Record<string, { nodeId: string; handleId: string }> = {};

    // Mark all handles as disconnected initially
    if (selectedNode.data.definition?.inputs) {
      selectedNode.data.definition.inputs.forEach((input) => {
        if (input.is_handle) {
          connectedHandles[input.name] = false;
        }
      });

      // Also check for dynamic inputs in the config
      if (selectedNode.data.config?.inputs) {
        selectedNode.data.config.inputs.forEach((input: InputDefinition) => {
          if (input.is_handle) {
            connectedHandles[input.name] = false;
          }
        });
      }
    }

    // Mark connected handles and store connection sources
    nodeInputEdges.forEach((edge) => {
      if (edge.targetHandle) {
        connectedHandles[edge.targetHandle] = true;
        if (edge.source && edge.sourceHandle) {
          sources[edge.targetHandle] = {
            nodeId: edge.source,
            handleId: edge.sourceHandle,
          };
        }
      }
    });

    setConnectedInputs(connectedHandles);
    setConnectionSources(sources);
  }, [selectedNode, edges]);

  // Helper function to check if a specific input is connected
  const isInputConnected = useCallback(
    (inputName: string) => {
      return !!connectedInputs[inputName];
    },
    [connectedInputs],
  );

  // Helper function to check if an input should be disabled
  // This handles both direct inputs and handle inputs
  const shouldDisableInput = useCallback(
    (inputName: string) => {
      // If this is a handle input, it should never be disabled
      if (inputName.endsWith("_handle")) {
        return false;
      }

      // For inputs with is_handle=true, check if they're connected directly
      const isDirectlyConnected = isInputConnected(inputName);

      // Debug logging for MCP inputs and any inputs with default values
      if (process.env.NODE_ENV === 'development' && (inputName.startsWith('tool_arg_') || inputName.includes('_'))) {
        console.log(`🔍 [DISABLE DEBUG] ${inputName}:`, {
          inputName,
          isDirectlyConnected,
          connectedInputs: Object.keys(connectedInputs).filter(key => connectedInputs[key]),
          allConnectedInputs: connectedInputs,
          willDisable: isDirectlyConnected
        });
      }

      // For MCP inputs and other handle inputs, only disable if directly connected
      if (isDirectlyConnected) {
        return true;
      }

      // For non-MCP inputs, check if there's a corresponding handle input
      // Skip this check for MCP inputs as they are handles themselves
      // Check if this is an MCP component input by looking at the selected node
      const isMCPInput = selectedNode && (
        selectedNode.data.type?.startsWith('MCP_') ||
        selectedNode.data.originalType?.startsWith('MCP_') ||
        inputName.startsWith('tool_arg_')
      );

      if (!isMCPInput) {
        const handleName = `${inputName}_handle`;
        const isHandleConnected = isInputConnected(handleName);

        // If the handle is connected, disable the direct input
        if (isHandleConnected) {
          return true;
        }
      }

      return false;
    },
    [connectedInputs, isInputConnected],
  );

  // Helper function to get the source node and handle for a connected input
  const getConnectedSource = useCallback(
    (inputName: string) => {
      return connectionSources[inputName] || null;
    },
    [connectionSources],
  );

  // Helper function to get detailed connection information
  const getConnectionInfo = useCallback(
    (inputName: string) => {
      const isConnected = isInputConnected(inputName);
      if (!isConnected) {
        return { isConnected: false };
      }

      const source = getConnectedSource(inputName);
      if (!source) {
        return { isConnected: true };
      }

      // Find the source node
      const sourceNode = nodes.find((node) => node.id === source.nodeId);
      if (!sourceNode) {
        return { isConnected: true, sourceNodeId: source.nodeId };
      }

      return {
        isConnected: true,
        sourceNodeId: source.nodeId,
        sourceNodeType: sourceNode.data.type,
        sourceNodeLabel: sourceNode.data.label || sourceNode.data.type,
      };
    },
    [isInputConnected, getConnectedSource, nodes],
  );

  return {
    connectedInputs,
    isInputConnected,
    shouldDisableInput,
    getConnectedSource,
    getConnectionInfo,
  };
}
