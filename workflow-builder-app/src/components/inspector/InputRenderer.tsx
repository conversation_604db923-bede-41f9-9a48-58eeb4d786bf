import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "./ValidationWrapper";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DynamicInput } from "@/components/ui/dynamic-input";
import { TemplateVariableInput } from "@/components/ui/template-variable-input";
import { TemplateVariableDisplay } from "@/components/ui/template-variable-display";
import { DynamicModelDropdown } from "@/components/ui/dynamic-model-dropdown";
import { cn } from "@/lib/utils";
import { hasTemplateVariables } from "@/utils/templateVariables";

// Import only the components still needed for special cases
import { HandleInput, CredentialInput } from "./inputs";
import { useInspector } from "./InspectorContext";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { ConnectedIndicator } from "../ui/connected-indicator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

// Helper function to get the selected provider for a node
function getSelectedProvider(nodeId: string, componentType: string, selectedNode: any): string | undefined {
  // Get the provider from the current node's config
  if (selectedNode && selectedNode.id === nodeId) {
    const provider = selectedNode.data?.config?.model_provider || selectedNode.data?.config?.inputs?.model_provider;
    console.log('🔍 Getting selected provider for node:', nodeId, 'Provider:', provider);
    return provider;
  }
  return undefined;
}

// Import the universal dynamic input system
import { EnhancedInputRenderer } from "./DynamicInputRenderer";
import { shouldUseDynamicInputSystem, logMigrationEvent } from "@/lib/inputSystemConfig";

interface InputRendererProps {
  inputDef: InputDefinition;
  value: any;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  connectionInfo?: {
    isConnected: boolean;
    sourceNodeId?: string;
    sourceNodeLabel?: string;
  };
  nodeId?: string;
  componentType?: string; // Add component type for configuration decisions
}

/**
 * Component for rendering different types of inputs based on their definition
 * Now uses the universal dynamic input system with backward compatibility
 */
export function InputRenderer({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  connectionInfo,
  nodeId,
  componentType,
}: InputRendererProps) {
  // SAFETY: Validate input definition
  if (!inputDef || !inputDef.name || !inputDef.input_type) {
    console.error('[INPUT RENDERER] Invalid input definition:', inputDef);
    return (
      <div className="mt-1 text-xs text-red-500">
        ❌ Invalid input definition
      </div>
    );
  }

  // Debug logging for MCP inputs with default values
  if (process.env.NODE_ENV === 'development' && componentType?.startsWith('MCP_') && inputDef.value !== undefined) {
    console.log(`🔍 [INPUT RENDERER DEBUG] ${inputDef.name}:`, {
      inputName: inputDef.name,
      componentType,
      nodeId,
      isDisabled,
      isConnected,
      hasDefault: inputDef.value !== undefined,
      defaultValue: inputDef.value,
      currentValue: value,
      inputType: inputDef.input_type,
      isHandle: inputDef.is_handle,
      inputDef
    });
  }

  const inputId = `config-${nodeId}-${inputDef.name}`;

  // SAFETY: Wrap everything in try-catch to prevent crashes
  try {
    // Check if we should use the dynamic system based on configuration
    const useDynamicSystem = shouldUseDynamicInputSystem(componentType, nodeId);



    // Log migration events for monitoring
    if (useDynamicSystem) {
      logMigrationEvent('Dynamic system used', {
        inputType: inputDef.input_type,
        componentType,
        nodeId,
        inputName: inputDef.name
      });
    }

    // If dynamic system is enabled, use it for all input types
    if (useDynamicSystem) {
      return (
        <UniversalInputRenderer
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          connectionInfo={connectionInfo}
          nodeId={nodeId}
          componentType={componentType}
        />
      );
    }
  } catch (error) {
    console.error('[INPUT RENDERER] Error in dynamic system decision:', error);
    // Fall through to emergency fallback
  }

  // CLEANED UP: Legacy hardcoded system removed - all input types now handled by universal dynamic system
  // Emergency fallback only (should never be reached in normal operation)
  console.warn('[INPUT RENDERER] Emergency fallback activated - this should not happen in normal operation');
  return (
    <div className="relative">
      <Input
        id={inputId}
        type="text"
        value={value ?? ""}
        onChange={(e) => onChange(inputDef.name, e.target.value)}
        placeholder={`${inputDef.display_name} (${inputDef.input_type})`}
        className="bg-background/50 mt-1 h-8 text-xs"
        disabled={isDisabled}
      />
      <div className="mt-1 text-xs text-red-500">
        ⚠️ Emergency fallback - contact support if you see this
      </div>
    </div>
  );
}

/**
 * Universal Input Renderer that handles all input types using the dynamic system
 * with fallbacks to legacy components for maximum compatibility
 */
function UniversalInputRenderer({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  connectionInfo,
  nodeId,
  componentType,
}: InputRendererProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;

  // Get the selected node from inspector context (with fallback)
  let selectedNode = null;
  try {
    const context = useInspector();
    selectedNode = context.selectedNode;
  } catch (error) {
    // Inspector context might not be available in some cases
    console.warn('Inspector context not available in InputRenderer');
  }

  // Special handling for certain input types that need custom rendering
  // These are kept as hardcoded for specific UI/UX requirements
  switch (inputDef.input_type) {
    case "credential":
      // Special handling for credential inputs
      console.log('🔍 Credential input detected:', {
        name: inputDef.name,
        inputType: inputDef.input_type,
        value
      });
      return (
        <CredentialInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId || 'unknown'}
        />
      );

    case "dropdown":
      // Check if this is a dynamic model dropdown
      if (inputDef.name === 'model_name') {
        console.log('🔍 Model dropdown check:', {
          name: inputDef.name,
          hasDynamicFiltering: !!(inputDef as any)._dynamicFiltering,
          inputDef: inputDef
        });

        if ((inputDef as any)._dynamicFiltering) {
          const selectedProvider = getSelectedProvider(nodeId || 'unknown', componentType || '', selectedNode);
          const providerIdMapping = (inputDef as any)._providerIdMapping;
          const allModels = inputDef.options || [];

          console.log('🔍 Model dropdown data:', {
            selectedProvider,
            providerIdMapping,
            allModelsCount: allModels.length,
            value
          });

          return (
            <ValidationWrapper inputDef={inputDef} value={value}>
              <div className="mt-1">
                <DynamicModelDropdown
                  value={value || ''}
                  onChange={(newValue) => onChange(inputDef.name, newValue)}
                  selectedProvider={selectedProvider}
                  providerIdMapping={providerIdMapping}
                  allModels={allModels.filter((m): m is string => typeof m === 'string')}
                  disabled={isDisabled}
                  placeholder={`Select ${inputDef.display_name || 'model'}...`}
                  className="w-full"
                />
                {isDisabled && isConnected && <ConnectedIndicator />}
              </div>
            </ValidationWrapper>
          );
        }
      }

      // Special handling for model_provider dropdown to ensure options are visible
      if (inputDef.name === 'model_provider') {
        console.log('🔍 Model provider dropdown options:', inputDef.options);
        return (
          <ValidationWrapper inputDef={inputDef} value={value}>
            <div className="mt-1">
              <Select
                value={value || ''}
                onValueChange={(newValue) => onChange(inputDef.name, newValue)}
                disabled={isDisabled}
              >
                <SelectTrigger className="bg-background/50 h-8 text-xs">
                  <SelectValue placeholder={`Select ${inputDef.display_name || 'provider'}...`} />
                </SelectTrigger>
                <SelectContent>
                  {(inputDef.options || []).map((option) => {
                    const optionValue = typeof option === 'string' ? option : option.value;
                    const optionLabel = typeof option === 'string' ? option : option.label;
                    return (
                      <SelectItem key={optionValue} value={optionValue} className="text-xs">
                        {optionLabel}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
              {isDisabled && isConnected && <ConnectedIndicator />}
            </div>
          </ValidationWrapper>
        );
      }

      // Handle regular dropdowns (non-agentic components)
      console.log('🔍 Regular dropdown detected:', {
        name: inputDef.name,
        inputType: inputDef.input_type,
        optionsCount: (inputDef.options || []).length,
        value
      });
      
      return (
        <ValidationWrapper inputDef={inputDef} value={value}>
          <div className="mt-1">
            <Select
              value={value || ''}
              onValueChange={(newValue) => onChange(inputDef.name, newValue)}
              disabled={isDisabled}
            >
              <SelectTrigger className="bg-background/50 h-8 text-xs">
                <SelectValue placeholder={`Select ${inputDef.display_name || 'option'}...`} />
              </SelectTrigger>
              <SelectContent>
                {(inputDef.options || []).map((option, index) => {
                  const optionValue = typeof option === 'string' ? option : option.value;
                  const optionLabel = typeof option === 'string' ? option : option.label;
                  return (
                    <SelectItem key={`${optionValue}-${index}`} value={optionValue} className="text-xs">
                      {optionLabel}
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {isDisabled && isConnected && <ConnectedIndicator />}
          </div>
        </ValidationWrapper>
      );

    case "button":
      return (
        <div className="mt-2">
          <Button
            id={inputId}
            onClick={() => onChange(inputDef.name, true)}
            disabled={isDisabled}
            className="h-8 w-full text-xs"
            variant="default"
          >
            {(inputDef as any).button_text || inputDef.display_name || "Click"}
          </Button>
        </div>
      );

    case "multiline":
    case "code":
      const stringValue = value ?? "";
      const hasTemplateVars = hasTemplateVariables(stringValue);

      return (
        <ValidationWrapper inputDef={inputDef} value={value} enableTemplateVariableValidation={true}>
          <div className="relative space-y-2">
            {/* Enhanced Template Variable Display for read-only/connected inputs */}
            {(isDisabled || isConnected) && hasTemplateVars && (
              <TemplateVariableDisplay
                value={stringValue}
                showHighlighting={true}
                showPreview={true}
                showIndicators={!isConnected} // Hide indicators when connected to reduce clutter
                maxLines={4}
                compact={false}
                className="mb-2"
                availableVariables={[]} // TODO: Connect to workflow context for available variables
              />
            )}

            {/* Input field - hidden when connected and has template variables */}
            {!(isConnected && hasTemplateVars) && (
              <>
                {hasTemplateVars || inputDef.input_type === 'multiline' ? (
                  // Use enhanced template variable input for multiline with template variables
                  <TemplateVariableInput
                    id={inputId}
                    value={stringValue}
                    onChange={(newValue) => onChange(inputDef.name, newValue)}
                    placeholder={`Enter ${inputDef.display_name}...`}
                    disabled={isDisabled}
                    multiline={true}
                    rows={4}
                    className={cn("bg-background/50 mt-1 text-xs", isDisabled && "opacity-50")}
                    showValidation={false} // Validation handled by ValidationWrapper
                    showSuggestions={true}
                    availableVariables={[]} // TODO: Connect to workflow context for available variables
                  />
                ) : (
                  // Use standard textarea for regular multiline
                  <Textarea
                    id={inputId}
                    value={stringValue}
                    onChange={(e) => onChange(inputDef.name, e.target.value)}
                    placeholder={`Enter ${inputDef.display_name}...`}
                    className={cn("bg-background/50 mt-1 text-xs", isDisabled && "opacity-50")}
                    rows={4}
                    disabled={isDisabled}
                  />
                )}
              </>
            )}
            {isDisabled && isConnected && <ConnectedIndicator />}
          </div>
        </ValidationWrapper>
      );

    case "credential":
      // Use the enhanced CredentialInput component with CredentialSelector integration
      return (
        <CredentialInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          nodeId={nodeId || 'unknown'}
        />
      );

    case "dynamic_handle":
      return (
        <DynamicInput
          inputDef={inputDef}
          currentValue={value}
          onChange={(name, value) => onChange(name, value)}
          isDisabled={isDisabled}
          isConnected={isConnected}
          minInputs={inputDef.min_handles || 0}
          maxInputs={inputDef.max_handles || 10}
          defaultInputs={inputDef.default_handles || 1}
        />
      );

    case "handle":
      return (
        <HandleInput
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          connectionInfo={connectionInfo}
          nodeId={nodeId}
        />
      );

    default:
      // For all other input types, use the enhanced dynamic system
      return (
        <EnhancedInputRenderer
          inputDef={inputDef}
          value={value}
          onChange={onChange}
          isDisabled={isDisabled}
          isConnected={isConnected}
          connectionInfo={connectionInfo}
          nodeId={nodeId}
        />
      );
  }
}
